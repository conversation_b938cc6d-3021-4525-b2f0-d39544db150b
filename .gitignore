# 环境变量文件
.env
.env.local
.env.*.local

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# Java相关
backend/target/
backend/*.jar
backend/*.war
backend/*.ear
backend/.mvn/wrapper/maven-wrapper.jar
backend/.mvn/wrapper/maven-wrapper.properties

# Node.js相关
frontend/node_modules/
frontend/dist/
frontend/.nuxt/
frontend/.output/
frontend/.vite/
frontend/.cache/

# 上传文件目录
uploads/
temp/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# Docker相关
.docker/

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 测试覆盖率报告
coverage/
*.lcov

# 依赖目录
node_modules/
vendor/

# 构建输出
build/
dist/
out/

# 缓存目录
.cache/
.parcel-cache/

# 运行时文件
*.pid
*.seed
*.pid.lock

# 错误日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 可选的REPL历史
.node_repl_history

# 输出的npm包
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env.test

# parcel-bundler缓存
.parcel-cache

# next.js构建输出
.next

# nuxt.js构建输出
.nuxt

# vuepress构建输出
.vuepress/dist

# Serverless目录
.serverless

# FuseBox缓存
.fusebox/

# DynamoDB本地文件
.dynamodb/
