#!/bin/bash

# 智能头像生成系统 - 项目初始化脚本
# 适用于Linux/Mac环境

echo "=== 智能头像生成系统项目初始化 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查工具函数
check_tool() {
    local tool_name=$1
    local command=$2
    
    echo -e "${YELLOW}检查 $tool_name...${NC}"
    if command -v $command &> /dev/null; then
        echo -e "${GREEN}✓ $tool_name 已安装${NC}"
        return 0
    else
        echo -e "${RED}✗ $tool_name 未安装或不在PATH中${NC}"
        return 1
    fi
}

# 检查必要工具
echo "检查开发环境..."
tools_ok=true

check_tool "Java" "java" || tools_ok=false
check_tool "Node.js" "node" || tools_ok=false
check_tool "npm" "npm" || tools_ok=false
check_tool "Git" "git" || tools_ok=false

if [ "$tools_ok" = false ]; then
    echo -e "${RED}请先安装缺失的工具后再运行此脚本${NC}"
    exit 1
fi

# 创建项目目录结构
echo -e "\n${YELLOW}创建项目目录结构...${NC}"

directories=("backend" "frontend" "docs" "scripts" "docker" "docker/mysql")

for dir in "${directories[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo -e "${GREEN}✓ 创建目录: $dir${NC}"
    fi
done

# 初始化后端项目
echo -e "\n${YELLOW}初始化Spring Boot后端项目...${NC}"

if [ ! -f "backend/pom.xml" ]; then
    echo -e "${CYAN}从Spring Initializr下载项目模板...${NC}"
    
    curl -s "https://start.spring.io/starter.zip" \
        -d dependencies=web,data-jpa,mysql,devtools,security,validation,actuator,data-redis \
        -d groupId=com.imageai \
        -d artifactId=avatar-generator \
        -d name=avatar-generator \
        -d description="AI Avatar Generator Backend" \
        -d packageName=com.imageai.avatargenerator \
        -d javaVersion=17 \
        -d type=maven-project \
        -o backend-temp.zip
    
    if [ $? -eq 0 ]; then
        unzip -q backend-temp.zip -d backend-temp
        mv backend-temp/*/* backend/ 2>/dev/null || mv backend-temp/* backend/
        rm -rf backend-temp backend-temp.zip
        echo -e "${GREEN}✓ Spring Boot项目创建成功${NC}"
    else
        echo -e "${RED}✗ 无法从Spring Initializr下载项目，请手动创建${NC}"
        echo -e "${YELLOW}请访问: https://start.spring.io/${NC}"
    fi
fi

# 初始化前端项目
echo -e "\n${YELLOW}初始化Vue3前端项目...${NC}"

if [ ! -f "frontend/package.json" ]; then
    cd frontend
    
    echo -e "${CYAN}创建Vue3项目...${NC}"
    # 使用非交互式方式创建Vue项目
    npm create vue@latest . -- --typescript --router --pinia --eslint --prettier --force
    
    if [ $? -eq 0 ]; then
        echo -e "${CYAN}安装依赖...${NC}"
        npm install
        
        echo -e "${CYAN}安装UI组件库和工具...${NC}"
        npm install element-plus @element-plus/icons-vue axios @vueuse/core
        
        echo -e "${GREEN}✓ Vue3项目创建成功${NC}"
    else
        echo -e "${RED}✗ Vue3项目创建失败${NC}"
    fi
    
    cd ..
fi

# 创建Docker配置文件
echo -e "\n${YELLOW}创建Docker配置文件...${NC}"

# Docker Compose文件
cat > docker-compose.yml << 'EOF'
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    container_name: avatar-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: avatar_generator
      MYSQL_USER: avatar_user
      MYSQL_PASSWORD: avatar_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:6-alpine
    container_name: avatar-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  backend:
    build: ./backend
    container_name: avatar-backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: avatar_user
      SPRING_DATASOURCE_PASSWORD: avatar_password
      SPRING_REDIS_HOST: redis
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    volumes:
      - ./uploads:/app/uploads

  frontend:
    build: ./frontend
    container_name: avatar-frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mysql_data:
  redis_data:
EOF

# 创建环境变量模板
cat > .env.example << 'EOF'
# AI服务配置
OPENAI_API_KEY=your_openai_api_key_here

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=avatar_generator
DB_USER=avatar_user
DB_PASSWORD=avatar_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 文件存储配置
UPLOAD_PATH=./uploads
CDN_BASE_URL=http://localhost:8080/uploads

# 应用配置
JWT_SECRET=your_jwt_secret_here
APP_ENV=development
EOF

# 创建数据库初始化脚本
cat > docker/mysql/init.sql << 'EOF'
-- 创建数据库
CREATE DATABASE IF NOT EXISTS avatar_generator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE avatar_generator;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建头像请求表
CREATE TABLE IF NOT EXISTS avatar_requests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    analysis_result JSON,
    prompt_text TEXT,
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建生成头像表
CREATE TABLE IF NOT EXISTS generated_avatars (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    request_id BIGINT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    ai_provider VARCHAR(50) NOT NULL,
    quality_score DECIMAL(3,2),
    is_selected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES avatar_requests(id) ON DELETE CASCADE
);

-- 创建用户偏好表
CREATE TABLE IF NOT EXISTS user_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    preferred_style VARCHAR(50),
    preferred_colors JSON,
    quality_threshold DECIMAL(3,2) DEFAULT 0.8,
    auto_select BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 插入测试数据
INSERT IGNORE INTO users (username, email, password_hash) VALUES 
('testuser', '<EMAIL>', '$2a$10$example_hash_here');
EOF

# 创建启动脚本
cat > scripts/start.sh << 'EOF'
#!/bin/bash
echo "启动智能头像生成系统..."

# 启动数据库和Redis
docker-compose up -d mysql redis

# 等待数据库启动
echo "等待数据库启动..."
sleep 10

# 启动后端
echo "启动后端服务..."
cd backend
./mvnw spring-boot:run &
BACKEND_PID=$!
cd ..

# 启动前端
echo "启动前端服务..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "系统启动完成!"
echo "前端地址: http://localhost:5173"
echo "后端地址: http://localhost:8080"
echo "API文档: http://localhost:8080/swagger-ui.html"

# 等待用户输入来停止服务
read -p "按Enter键停止所有服务..."

# 停止服务
kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
docker-compose down

echo "所有服务已停止"
EOF

# 设置脚本执行权限
chmod +x scripts/start.sh

echo -e "\n${GREEN}✓ 项目初始化完成!${NC}"
echo -e "\n${YELLOW}下一步操作:${NC}"
echo -e "${CYAN}1. 复制 .env.example 为 .env 并填入你的API密钥${NC}"
echo -e "${CYAN}2. 运行 'docker-compose up -d mysql redis' 启动数据库${NC}"
echo -e "${CYAN}3. 在backend目录运行 './mvnw spring-boot:run' 启动后端${NC}"
echo -e "${CYAN}4. 在frontend目录运行 'npm run dev' 启动前端${NC}"
echo -e "${CYAN}5. 或者直接运行 'chmod +x scripts/start.sh && ./scripts/start.sh' 一键启动所有服务${NC}"

echo -e "\n${YELLOW}项目文档:${NC}"
echo -e "${CYAN}- README.md: 项目概述${NC}"
echo -e "${CYAN}- TECHNICAL_DESIGN.md: 技术设计方案${NC}"
echo -e "${CYAN}- DEVELOPMENT_GUIDE.md: 开发指南${NC}"
echo -e "${CYAN}- API_DESIGN.md: API接口文档${NC}"
