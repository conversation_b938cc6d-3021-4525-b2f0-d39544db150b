# 开发指南

## 1. 环境准备

### 1.1 开发环境要求
- **Java**: JDK 17+
- **Node.js**: 18.0+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Maven**: 3.8+
- **Git**: 最新版本

### 1.2 IDE推荐
- **后端**: IntelliJ IDEA Ultimate
- **前端**: VS Code + Vue插件
- **数据库**: DataGrip 或 MySQL Workbench

### 1.3 必要的API密钥
- **OpenAI API Key**: 用于GPT-4文本分析和DALL-E图像生成
- **云存储服务**: 阿里云OSS或AWS S3
- **可选**: Midjourney API、Stability AI API

## 2. 项目初始化步骤

### 2.1 创建后端项目

```bash
# 1. 使用Spring Initializr创建项目
# 访问 https://start.spring.io/
# 选择以下依赖：
# - Spring Web
# - Spring Data JPA
# - MySQL Driver
# - Spring Boot DevTools
# - Spring Security
# - Validation
# - Spring Boot Actuator

# 2. 或使用命令行创建
curl https://start.spring.io/starter.zip \
  -d dependencies=web,data-jpa,mysql,devtools,security,validation,actuator \
  -d groupId=com.imageai \
  -d artifactId=avatar-generator \
  -d name=avatar-generator \
  -d description="AI Avatar Generator Backend" \
  -d packageName=com.imageai.avatargenerator \
  -d javaVersion=17 \
  -o backend.zip

# 3. 解压并移动到backend目录
unzip backend.zip -d backend/
```

### 2.2 创建前端项目

```bash
# 1. 创建Vue3项目
npm create vue@latest frontend

# 选择以下选项：
# ✅ TypeScript
# ✅ Router
# ✅ Pinia
# ✅ ESLint
# ✅ Prettier

# 2. 进入项目目录并安装依赖
cd frontend
npm install

# 3. 安装额外依赖
npm install element-plus @element-plus/icons-vue
npm install axios
npm install tailwindcss @tailwindcss/forms
npm install @vueuse/core
```

### 2.3 数据库初始化

```sql
-- 1. 创建数据库
CREATE DATABASE avatar_generator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 创建用户（可选）
CREATE USER 'avatar_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON avatar_generator.* TO 'avatar_user'@'localhost';
FLUSH PRIVILEGES;
```

## 3. 核心功能开发顺序

### Phase 1: 基础框架 (第1-2周)

#### 3.1 后端基础设置

```java
// 1. 配置文件 application.yml
spring:
  datasource:
    url: ********************************************
    username: avatar_user
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  redis:
    host: localhost
    port: 6379
    database: 0

# AI服务配置
ai:
  openai:
    api-key: ${OPENAI_API_KEY}
    base-url: https://api.openai.com/v1
  
  image-storage:
    type: local # 或 oss, s3
    local-path: ./uploads
```

#### 3.2 核心实体类

```java
// User.java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String username;
    
    @Column(unique = true, nullable = false)
    private String email;
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    // getters and setters
}

// AvatarRequest.java
@Entity
@Table(name = "avatar_requests")
public class AvatarRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @Column(nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(columnDefinition = "JSON")
    private String analysisResult;
    
    @Column(columnDefinition = "TEXT")
    private String promptText;
    
    @Enumerated(EnumType.STRING)
    private RequestStatus status = RequestStatus.PENDING;
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    // getters and setters
}
```

#### 3.3 前端基础组件

```vue
<!-- InputForm.vue -->
<template>
  <div class="input-form">
    <el-form :model="form" :rules="rules" ref="formRef">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" placeholder="请输入您的姓名" />
      </el-form-item>
      
      <el-form-item label="个人简介" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请详细描述您的职业、兴趣、特长等信息"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm" :loading="loading">
          生成头像
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useAvatarStore } from '@/stores/avatar'

const avatarStore = useAvatarStore()
const formRef = ref()
const loading = ref(false)

const form = reactive({
  name: '',
  description: ''
})

const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入个人简介', trigger: 'blur' },
    { min: 10, max: 500, message: '简介长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      try {
        await avatarStore.submitGenerationRequest(form)
      } finally {
        loading.value = false
      }
    }
  })
}
</script>
```

### Phase 2: 核心功能 (第3-5周)

#### 3.4 文本分析服务

```java
@Service
public class TextAnalysisService {
    
    @Autowired
    private OpenAIService openAIService;
    
    public AnalysisResult analyzeUserInput(String name, String description) {
        // 构建分析提示词
        String prompt = buildAnalysisPrompt(name, description);
        
        // 调用GPT-4进行分析
        String analysisJson = openAIService.chatCompletion(prompt);
        
        // 解析结果
        return parseAnalysisResult(analysisJson);
    }
    
    private String buildAnalysisPrompt(String name, String description) {
        return String.format("""
            请分析以下用户信息，提取关键特征用于生成专业头像：
            
            姓名：%s
            简介：%s
            
            请从以下维度进行分析并返回JSON格式：
            1. 推测的性别倾向 (gender)
            2. 估计的年龄段 (ageRange)
            3. 职业领域 (profession)
            4. 个性特征 (personality)
            5. 兴趣爱好 (interests)
            6. 适合的头像风格 (avatarStyle)
            7. 关键描述词 (keywords)
            
            返回格式：
            {
              "gender": "male/female/neutral",
              "ageRange": "20-30/30-40/40-50/50+",
              "profession": "技术/商务/创意/学术/其他",
              "personality": ["专业", "创新", "稳重"],
              "interests": ["技术", "旅游", "音乐"],
              "avatarStyle": "商务风/创意风/学术风/技术风",
              "keywords": ["专业", "自信", "技术感"]
            }
            """, name, description);
    }
}
```

#### 3.5 AI图像生成服务

```java
@Service
public class AIImageGenerationService {
    
    @Value("${ai.openai.api-key}")
    private String openaiApiKey;
    
    public List<GeneratedImage> generateAvatars(AnalysisResult analysis) {
        List<GeneratedImage> results = new ArrayList<>();
        
        // 生成多个不同风格的提示词
        List<String> prompts = generateMultiplePrompts(analysis);
        
        for (String prompt : prompts) {
            try {
                GeneratedImage image = callDallE(prompt);
                if (image != null) {
                    results.add(image);
                }
            } catch (Exception e) {
                log.error("Failed to generate image with prompt: " + prompt, e);
            }
        }
        
        return results;
    }
    
    private List<String> generateMultiplePrompts(AnalysisResult analysis) {
        List<String> prompts = new ArrayList<>();
        
        // 基础商务风格
        prompts.add(buildBusinessStylePrompt(analysis));
        
        // 创意风格
        prompts.add(buildCreativeStylePrompt(analysis));
        
        // 专业风格
        prompts.add(buildProfessionalStylePrompt(analysis));
        
        return prompts;
    }
    
    private String buildBusinessStylePrompt(AnalysisResult analysis) {
        return String.format("""
            Professional business headshot portrait of a %s %s professional, 
            %s, working in %s field, 
            clean background, studio lighting, 
            high quality, professional photography, 
            confident expression, business attire,
            %s
            """, 
            analysis.getGender(),
            analysis.getAgeRange(),
            String.join(", ", analysis.getPersonality()),
            analysis.getProfession(),
            String.join(", ", analysis.getKeywords())
        );
    }
}
```

## 4. 测试策略

### 4.1 单元测试

```java
@SpringBootTest
class TextAnalysisServiceTest {
    
    @Autowired
    private TextAnalysisService textAnalysisService;
    
    @Test
    void testAnalyzeUserInput() {
        String name = "张伟";
        String description = "我是一名软件工程师，专注于Java后端开发，喜欢技术创新";
        
        AnalysisResult result = textAnalysisService.analyzeUserInput(name, description);
        
        assertThat(result.getProfession()).isEqualTo("技术");
        assertThat(result.getKeywords()).contains("技术");
    }
}
```

### 4.2 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class AvatarControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testGenerateAvatar() {
        GenerationRequest request = new GenerationRequest();
        request.setName("测试用户");
        request.setDescription("软件开发工程师");
        
        ResponseEntity<GenerationResponse> response = restTemplate.postForEntity(
            "/api/avatar/generate", request, GenerationResponse.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
```

## 5. 部署指南

### 5.1 Docker配置

```dockerfile
# backend/Dockerfile
FROM openjdk:17-jdk-slim
COPY target/avatar-generator-*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
EXPOSE 80
```

### 5.2 Docker Compose

```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: avatar_generator
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_REDIS_HOST: redis

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mysql_data:
```

## 6. 开发最佳实践

### 6.1 代码规范
- 使用统一的代码格式化工具
- 遵循RESTful API设计原则
- 编写清晰的注释和文档
- 使用有意义的变量和方法名

### 6.2 性能优化
- 合理使用缓存
- 数据库查询优化
- 异步处理耗时操作
- 图片压缩和CDN加速

### 6.3 安全考虑
- 输入验证和过滤
- API访问限制
- 敏感信息加密
- 定期安全审计

这个开发指南为您提供了完整的项目搭建和开发流程。建议按照Phase的顺序逐步实现，确保每个阶段的功能都经过充分测试后再进入下一阶段。

## 7. 下一步行动计划

1. **立即开始**: 按照环境准备章节配置开发环境
2. **第一周**: 完成项目初始化和基础框架搭建
3. **第二周**: 实现核心的文本分析功能
4. **第三周**: 集成AI图像生成服务
5. **第四周**: 完善前端界面和用户体验
6. **第五周**: 测试、优化和部署准备

详细的API接口设计请参考 `API_DESIGN.md` 文档。
