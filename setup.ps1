# 智能头像生成系统 - 项目初始化脚本
# PowerShell脚本，适用于Windows环境

Write-Host "=== 智能头像生成系统项目初始化 ===" -ForegroundColor Green

# 检查必要的工具
function Check-Tool {
    param($toolName, $command)
    Write-Host "检查 $toolName..." -ForegroundColor Yellow
    try {
        & $command --version | Out-Null
        Write-Host "✓ $toolName 已安装" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ $toolName 未安装或不在PATH中" -ForegroundColor Red
        return $false
    }
}

# 检查环境
$javaOk = Check-Tool "Java" "java"
$nodeOk = Check-Tool "Node.js" "node"
$npmOk = Check-Tool "npm" "npm"
$gitOk = Check-Tool "Git" "git"

if (-not ($javaOk -and $nodeOk -and $npmOk -and $gitOk)) {
    Write-Host "请先安装缺失的工具后再运行此脚本" -ForegroundColor Red
    exit 1
}

# 创建项目目录结构
Write-Host "`n创建项目目录结构..." -ForegroundColor Yellow

$directories = @(
    "backend",
    "frontend", 
    "docs",
    "scripts",
    "docker"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir | Out-Null
        Write-Host "✓ 创建目录: $dir" -ForegroundColor Green
    }
}

# 初始化后端项目
Write-Host "`n初始化Spring Boot后端项目..." -ForegroundColor Yellow

if (-not (Test-Path "backend/pom.xml")) {
    $springInitializrUrl = "https://start.spring.io/starter.zip"
    $params = @{
        "dependencies" = "web,data-jpa,mysql,devtools,security,validation,actuator,data-redis"
        "groupId" = "com.imageai"
        "artifactId" = "avatar-generator"
        "name" = "avatar-generator"
        "description" = "AI Avatar Generator Backend"
        "packageName" = "com.imageai.avatargenerator"
        "javaVersion" = "17"
        "type" = "maven-project"
    }
    
    $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
    $fullUrl = "$springInitializrUrl?$queryString"
    
    try {
        Write-Host "从Spring Initializr下载项目模板..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri $fullUrl -OutFile "backend-temp.zip"
        
        # 解压到临时目录
        Expand-Archive -Path "backend-temp.zip" -DestinationPath "backend-temp" -Force
        
        # 移动文件到backend目录
        Get-ChildItem "backend-temp" | Move-Item -Destination "backend" -Force
        
        # 清理临时文件
        Remove-Item "backend-temp.zip" -Force
        Remove-Item "backend-temp" -Recurse -Force
        
        Write-Host "✓ Spring Boot项目创建成功" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ 无法从Spring Initializr下载项目，请手动创建" -ForegroundColor Red
        Write-Host "请访问: https://start.spring.io/" -ForegroundColor Yellow
    }
}

# 初始化前端项目
Write-Host "`n初始化Vue3前端项目..." -ForegroundColor Yellow

if (-not (Test-Path "frontend/package.json")) {
    Set-Location frontend
    
    try {
        # 创建Vue3项目
        Write-Host "创建Vue3项目..." -ForegroundColor Cyan
        npm create vue@latest . -- --typescript --router --pinia --eslint --prettier
        
        Write-Host "安装依赖..." -ForegroundColor Cyan
        npm install
        
        # 安装额外依赖
        Write-Host "安装UI组件库和工具..." -ForegroundColor Cyan
        npm install element-plus @element-plus/icons-vue axios @vueuse/core
        
        Write-Host "✓ Vue3项目创建成功" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Vue3项目创建失败" -ForegroundColor Red
    }
    finally {
        Set-Location ..
    }
}

# 创建Docker配置
Write-Host "`n创建Docker配置文件..." -ForegroundColor Yellow

# Docker Compose文件
$dockerComposeContent = @"
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    container_name: avatar-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: avatar_generator
      MYSQL_USER: avatar_user
      MYSQL_PASSWORD: avatar_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:6-alpine
    container_name: avatar-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  backend:
    build: ./backend
    container_name: avatar-backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: avatar_user
      SPRING_DATASOURCE_PASSWORD: avatar_password
      SPRING_REDIS_HOST: redis
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    volumes:
      - ./uploads:/app/uploads

  frontend:
    build: ./frontend
    container_name: avatar-frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mysql_data:
  redis_data:
"@

$dockerComposeContent | Out-File -FilePath "docker-compose.yml" -Encoding UTF8

# 创建环境变量模板
$envTemplate = @"
# AI服务配置
OPENAI_API_KEY=your_openai_api_key_here

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=avatar_generator
DB_USER=avatar_user
DB_PASSWORD=avatar_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 文件存储配置
UPLOAD_PATH=./uploads
CDN_BASE_URL=http://localhost:8080/uploads

# 应用配置
JWT_SECRET=your_jwt_secret_here
APP_ENV=development
"@

$envTemplate | Out-File -FilePath ".env.example" -Encoding UTF8

# 创建数据库初始化脚本
if (-not (Test-Path "docker/mysql")) {
    New-Item -ItemType Directory -Path "docker/mysql" -Force | Out-Null
}

$sqlInit = @"
-- 创建数据库
CREATE DATABASE IF NOT EXISTS avatar_generator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE avatar_generator;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建头像请求表
CREATE TABLE IF NOT EXISTS avatar_requests (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    analysis_result JSON,
    prompt_text TEXT,
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建生成头像表
CREATE TABLE IF NOT EXISTS generated_avatars (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    request_id BIGINT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    ai_provider VARCHAR(50) NOT NULL,
    quality_score DECIMAL(3,2),
    is_selected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES avatar_requests(id) ON DELETE CASCADE
);

-- 创建用户偏好表
CREATE TABLE IF NOT EXISTS user_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    preferred_style VARCHAR(50),
    preferred_colors JSON,
    quality_threshold DECIMAL(3,2) DEFAULT 0.8,
    auto_select BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 插入测试数据
INSERT IGNORE INTO users (username, email, password_hash) VALUES 
('testuser', '<EMAIL>', '$2a$10$example_hash_here');
"@

$sqlInit | Out-File -FilePath "docker/mysql/init.sql" -Encoding UTF8

# 创建启动脚本
$startScript = @"
#!/bin/bash
echo "启动智能头像生成系统..."

# 启动数据库和Redis
docker-compose up -d mysql redis

# 等待数据库启动
echo "等待数据库启动..."
sleep 10

# 启动后端
echo "启动后端服务..."
cd backend
./mvnw spring-boot:run &
BACKEND_PID=$!
cd ..

# 启动前端
echo "启动前端服务..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "系统启动完成!"
echo "前端地址: http://localhost:5173"
echo "后端地址: http://localhost:8080"
echo "API文档: http://localhost:8080/swagger-ui.html"

# 等待用户输入来停止服务
read -p "按Enter键停止所有服务..."

# 停止服务
kill $BACKEND_PID $FRONTEND_PID
docker-compose down

echo "所有服务已停止"
"@

$startScript | Out-File -FilePath "scripts/start.sh" -Encoding UTF8

# 创建Windows启动脚本
$startBat = @"
@echo off
echo 启动智能头像生成系统...

REM 启动数据库和Redis
docker-compose up -d mysql redis

REM 等待数据库启动
echo 等待数据库启动...
timeout /t 10 /nobreak

REM 启动后端
echo 启动后端服务...
start "Backend" cmd /k "cd backend && mvnw.cmd spring-boot:run"

REM 启动前端
echo 启动前端服务...
start "Frontend" cmd /k "cd frontend && npm run dev"

echo 系统启动完成!
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:8080
echo API文档: http://localhost:8080/swagger-ui.html

pause
"@

$startBat | Out-File -FilePath "scripts/start.bat" -Encoding UTF8

Write-Host "`n✓ 项目初始化完成!" -ForegroundColor Green
Write-Host "`n下一步操作:" -ForegroundColor Yellow
Write-Host "1. 复制 .env.example 为 .env 并填入你的API密钥" -ForegroundColor Cyan
Write-Host "2. 运行 'docker-compose up -d mysql redis' 启动数据库" -ForegroundColor Cyan
Write-Host "3. 在backend目录运行 './mvnw spring-boot:run' 启动后端" -ForegroundColor Cyan
Write-Host "4. 在frontend目录运行 'npm run dev' 启动前端" -ForegroundColor Cyan
Write-Host "5. 或者直接运行 'scripts/start.bat' 一键启动所有服务" -ForegroundColor Cyan

Write-Host "`n项目文档:" -ForegroundColor Yellow
Write-Host "- README.md: 项目概述" -ForegroundColor Cyan
Write-Host "- TECHNICAL_DESIGN.md: 技术设计方案" -ForegroundColor Cyan
Write-Host "- DEVELOPMENT_GUIDE.md: 开发指南" -ForegroundColor Cyan
Write-Host "- API_DESIGN.md: API接口文档" -ForegroundColor Cyan
