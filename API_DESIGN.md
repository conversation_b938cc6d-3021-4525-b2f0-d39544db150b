# API接口设计文档

## 1. 接口概览

### 1.1 基础信息
- **Base URL**: `http://localhost:8080/api`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 1.3 错误码定义

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 200 | 成功 | 200 |
| 400 | 请求参数错误 | 400 |
| 401 | 未授权 | 401 |
| 403 | 禁止访问 | 403 |
| 404 | 资源不存在 | 404 |
| 429 | 请求过于频繁 | 429 |
| 500 | 服务器内部错误 | 500 |
| 1001 | 文本分析失败 | 400 |
| 1002 | AI服务不可用 | 503 |
| 1003 | 图像生成失败 | 500 |

## 2. 用户认证接口

### 2.1 用户注册

**接口**: `POST /auth/register`

**请求参数**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "userId": 1,
    "username": "testuser",
    "email": "<EMAIL>"
  }
}
```

### 2.2 用户登录

**接口**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "testuser",
  "password": "password123"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600,
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

## 3. 头像生成核心接口

### 3.1 提交生成请求

**接口**: `POST /avatar/generate`

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "name": "张伟",
  "description": "我是一名软件工程师，专注于Java后端开发，有5年工作经验。喜欢技术创新，业余时间喜欢阅读和旅游。",
  "preferences": {
    "style": "business", // business, creative, academic, tech
    "quality": "high",   // standard, high, premium
    "count": 3          // 生成数量，1-5
  }
}
```

**响应**:
```json
{
  "code": 200,
  "message": "请求已提交",
  "data": {
    "requestId": "req_123456789",
    "estimatedTime": 30, // 预计完成时间（秒）
    "status": "PROCESSING"
  }
}
```

### 3.2 查询生成状态

**接口**: `GET /avatar/status/{requestId}`

**请求头**:
```
Authorization: Bearer {token}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "requestId": "req_123456789",
    "status": "COMPLETED", // PENDING, PROCESSING, COMPLETED, FAILED
    "progress": 100,       // 进度百分比
    "analysisResult": {
      "gender": "male",
      "ageRange": "25-35",
      "profession": "技术",
      "personality": ["专业", "创新", "稳重"],
      "interests": ["技术", "阅读", "旅游"],
      "avatarStyle": "商务风",
      "keywords": ["专业", "自信", "技术感"]
    },
    "generatedAvatars": [
      {
        "id": "avatar_001",
        "imageUrl": "https://cdn.example.com/avatars/avatar_001.jpg",
        "thumbnailUrl": "https://cdn.example.com/avatars/thumb_avatar_001.jpg",
        "style": "business",
        "qualityScore": 0.92,
        "aiProvider": "openai"
      }
    ]
  }
}
```

### 3.3 重新生成头像

**接口**: `POST /avatar/regenerate`

**请求参数**:
```json
{
  "requestId": "req_123456789",
  "adjustments": {
    "style": "creative",
    "keywords": ["年轻", "活力", "创新"],
    "excludeStyles": ["formal", "serious"]
  }
}
```

**响应**:
```json
{
  "code": 200,
  "message": "重新生成请求已提交",
  "data": {
    "newRequestId": "req_987654321",
    "estimatedTime": 25
  }
}
```

## 4. 头像管理接口

### 4.1 获取用户头像列表

**接口**: `GET /avatar/list`

**请求参数**:
```
page=1&size=10&status=COMPLETED
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "page": 1,
    "size": 10,
    "items": [
      {
        "requestId": "req_123456789",
        "name": "张伟",
        "description": "软件工程师...",
        "status": "COMPLETED",
        "createdAt": "2024-01-01T12:00:00Z",
        "avatarCount": 3,
        "selectedAvatar": {
          "id": "avatar_001",
          "thumbnailUrl": "https://cdn.example.com/avatars/thumb_avatar_001.jpg"
        }
      }
    ]
  }
}
```

### 4.2 下载头像

**接口**: `GET /avatar/download/{avatarId}`

**请求参数**:
```
format=jpg&size=512  // 可选：jpg/png, 尺寸：256/512/1024
```

**响应**: 直接返回图片文件流

### 4.3 选择头像

**接口**: `POST /avatar/select`

**请求参数**:
```json
{
  "requestId": "req_123456789",
  "avatarId": "avatar_001"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "头像选择成功",
  "data": {
    "selectedAvatar": {
      "id": "avatar_001",
      "imageUrl": "https://cdn.example.com/avatars/avatar_001.jpg"
    }
  }
}
```

## 5. 用户偏好接口

### 5.1 获取用户偏好

**接口**: `GET /user/preferences`

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "preferredStyle": "business",
    "preferredColors": ["blue", "gray", "white"],
    "qualityThreshold": 0.8,
    "autoSelect": false,
    "notifications": {
      "email": true,
      "push": false
    }
  }
}
```

### 5.2 更新用户偏好

**接口**: `PUT /user/preferences`

**请求参数**:
```json
{
  "preferredStyle": "creative",
  "preferredColors": ["blue", "green"],
  "qualityThreshold": 0.85,
  "autoSelect": true
}
```

## 6. 系统管理接口

### 6.1 获取系统状态

**接口**: `GET /system/status`

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "aiServices": {
      "openai": {
        "status": "available",
        "responseTime": 1200,
        "successRate": 0.98
      },
      "midjourney": {
        "status": "maintenance",
        "responseTime": null,
        "successRate": 0.0
      }
    },
    "queueStatus": {
      "pending": 5,
      "processing": 2,
      "avgWaitTime": 15
    }
  }
}
```

### 6.2 获取使用统计

**接口**: `GET /user/statistics`

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalRequests": 25,
    "successfulGenerations": 23,
    "favoriteStyle": "business",
    "avgQualityScore": 0.89,
    "monthlyUsage": {
      "current": 8,
      "limit": 50,
      "resetDate": "2024-02-01"
    }
  }
}
```

## 7. WebSocket实时通信

### 7.1 连接地址
```
ws://localhost:8080/ws/avatar-progress
```

### 7.2 消息格式

**进度更新**:
```json
{
  "type": "progress",
  "requestId": "req_123456789",
  "progress": 45,
  "stage": "analyzing", // analyzing, generating, processing, completed
  "message": "正在分析用户信息..."
}
```

**生成完成**:
```json
{
  "type": "completed",
  "requestId": "req_123456789",
  "avatarCount": 3,
  "message": "头像生成完成"
}
```

**错误通知**:
```json
{
  "type": "error",
  "requestId": "req_123456789",
  "errorCode": 1003,
  "message": "图像生成失败，请重试"
}
```

## 8. 接口调用示例

### 8.1 完整的头像生成流程

```javascript
// 1. 用户登录
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'testuser',
    password: 'password123'
  })
});
const { token } = await loginResponse.json();

// 2. 提交生成请求
const generateResponse = await fetch('/api/avatar/generate', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: '张伟',
    description: '软件工程师，专注Java开发...',
    preferences: {
      style: 'business',
      quality: 'high',
      count: 3
    }
  })
});
const { requestId } = await generateResponse.json();

// 3. 建立WebSocket连接监听进度
const ws = new WebSocket('ws://localhost:8080/ws/avatar-progress');
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.requestId === requestId) {
    console.log('Progress:', data.progress);
  }
};

// 4. 轮询查询状态（备选方案）
const checkStatus = async () => {
  const response = await fetch(`/api/avatar/status/${requestId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  const result = await response.json();
  return result.data;
};
```

这个API设计文档提供了完整的接口规范，包括认证、头像生成、管理和实时通信等所有功能。建议在开发过程中严格按照这个规范实现，确保前后端的一致性。
